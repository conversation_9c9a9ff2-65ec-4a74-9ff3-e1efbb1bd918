'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAccount, useWalletClient } from 'wagmi';
import {
  Container,
  Title,
  Text,
  Stack,
  Paper,
  Group,
  ThemeIcon,
  Button,
  Alert,
  Card,
  Badge,
  Divider,
  Center,
  Loader,
  SimpleGrid,
  TextInput,
  NumberInput,
  Tabs,
  Table,
  ActionIcon,
  Modal,
} from '@mantine/core';
import {
  IconGift,
  IconAlertCircle,
  IconCheck,
  IconWallet,
  IconArrowLeft,
  IconCoin,
  IconPlus,
  IconTrash,
  IconDownload,
  IconShield,
  IconDatabase,
} from '@tabler/icons-react';
import { AppShellLayout } from '@/components/Layout/AppShell';
import { initializeSDK, sdk } from '@/lib/odude';
import { notifications } from '@mantine/notifications';
import { ethers } from 'ethers';
import { useDisclosure } from '@mantine/hooks';

interface AirdropData {
  airdropId: number;
  tokenAddress: string;
  totalAmount: bigint;
  perUserShare: bigint;
  remainingAmount: bigint;
  isActive: boolean;
  createdAt: number;
}

interface TldAirdropData {
  tldName: string;
  isOwner: boolean;
  owner: string;
  airdrops: AirdropData[];
}

interface PageProps {
  params: Promise<{
    'tld-name': string;
  }> | {
    'tld-name': string;
  };
}

export default function AirdropManagementPage({ params }: PageProps) {
  const [tldName, setTldName] = useState<string>('');
  
  // Handle async params (Next.js 15 compatibility)
  useEffect(() => {
    const getParams = async () => {
      try {
        const resolvedParams = params instanceof Promise ? await params : params;
        setTldName(resolvedParams['tld-name']);
      } catch (error) {
        console.error('Error resolving params:', error);
        setTldName('');
      }
    };
    getParams();
  }, [params]);

  const { isConnected, address } = useAccount();
  const { data: walletClient } = useWalletClient();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [airdropData, setAirdropData] = useState<TldAirdropData | null>(null);
  const [sdkReady, setSdkReady] = useState(false);
  
  // Create airdrop modal
  const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false);
  const [creating, setCreating] = useState(false);
  const [newTokenAddress, setNewTokenAddress] = useState('');
  const [newTotalAmount, setNewTotalAmount] = useState('');
  const [newPerUserShare, setNewPerUserShare] = useState('');

  const fetchAirdropData = useCallback(async () => {
    if (!tldName) return;

    try {
      setLoading(true);
      setError(null);

      const decodedTldName = decodeURIComponent(tldName);
      console.log('Fetching airdrop data for TLD:', decodedTldName);

      // First check if user owns the TLD
      const tldTokenId = await sdk.tld().getTLDId(decodedTldName);
      const tldOwner = await sdk.tld().getTLDOwner(tldTokenId);
      const isOwner = address?.toLowerCase() === tldOwner.toLowerCase();

      // Get airdrop count and IDs for this TLD
      const airdropCount = await sdk.rwairdrop().getTLDAirdropCount(decodedTldName);
      const airdropIds = await sdk.rwairdrop().getTLDAirdropIds(decodedTldName);

      // Get detailed information for each airdrop
      const airdrops: AirdropData[] = [];
      for (const airdropId of airdropIds) {
        try {
          const airdropInfo = await sdk.rwairdrop().getAirdropInfo(decodedTldName, airdropId);
          const remainingAmount = await sdk.rwairdrop().getRemainingAirdrop(decodedTldName, airdropId);
          
          airdrops.push({
            airdropId: Number(airdropId),
            tokenAddress: airdropInfo.tokenAddress,
            totalAmount: airdropInfo.totalAmount,
            perUserShare: airdropInfo.perUserShare,
            remainingAmount: remainingAmount,
            isActive: airdropInfo.isActive,
            createdAt: Number(airdropInfo.createdAt),
          });
        } catch (err) {
          console.warn(`Failed to fetch airdrop ${airdropId}:`, err);
        }
      }

      setAirdropData({
        tldName: decodedTldName,
        isOwner,
        owner: tldOwner,
        airdrops,
      });

    } catch (error) {
      console.error('Failed to fetch airdrop data:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch airdrop information');
    } finally {
      setLoading(false);
    }
  }, [tldName, address]);

  useEffect(() => {
    if (!isConnected) {
      router.push('/');
      return;
    }

    initializeSDK()
      .then(() => setSdkReady(true))
      .catch((err) => setError(err.message));
  }, [isConnected, router]);

  useEffect(() => {
    if (sdkReady && tldName) {
      fetchAirdropData();
    }
  }, [sdkReady, tldName, fetchAirdropData]);

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const formatAmount = (amount: bigint) => {
    return ethers.formatEther(amount);
  };

  const handleCreateAirdrop = async () => {
    if (!airdropData || !walletClient || !newTokenAddress || !newTotalAmount || !newPerUserShare) {
      notifications.show({
        title: 'Invalid Input',
        message: 'Please fill in all required fields',
        color: 'red',
      });
      return;
    }

    setCreating(true);
    try {
      // Connect the wallet signer to the SDK
      sdk.connectSigner(walletClient);

      const totalAmountWei = ethers.parseEther(newTotalAmount);
      const perUserShareWei = ethers.parseEther(newPerUserShare);

      const tx = await sdk.rwairdrop().createAirdrop(
        airdropData.tldName,
        newTokenAddress,
        totalAmountWei,
        perUserShareWei
      );

      await tx.wait();

      notifications.show({
        title: 'Airdrop Created',
        message: 'Airdrop created successfully',
        color: 'green',
      });

      // Reset form and close modal
      setNewTokenAddress('');
      setNewTotalAmount('');
      setNewPerUserShare('');
      closeCreateModal();
      
      // Refresh data
      await fetchAirdropData();

    } catch (error) {
      console.error('Failed to create airdrop:', error);
      notifications.show({
        title: 'Creation Failed',
        message: error instanceof Error ? error.message : 'Failed to create airdrop',
        color: 'red',
      });
    } finally {
      setCreating(false);
    }
  };

  const handleWithdrawAirdrop = async (airdropId: number) => {
    if (!airdropData || !walletClient) return;

    try {
      // Connect the wallet signer to the SDK
      sdk.connectSigner(walletClient);

      const tx = await sdk.rwairdrop().withdrawAirdrop(airdropData.tldName, airdropId);
      await tx.wait();

      notifications.show({
        title: 'Airdrop Withdrawn',
        message: 'Remaining tokens withdrawn successfully',
        color: 'green',
      });

      // Refresh data
      await fetchAirdropData();

    } catch (error) {
      console.error('Failed to withdraw airdrop:', error);
      notifications.show({
        title: 'Withdrawal Failed',
        message: error instanceof Error ? error.message : 'Failed to withdraw airdrop',
        color: 'red',
      });
    }
  };

  if (!isConnected || !tldName) {
    return null; // Will redirect or still loading params
  }

  return (
    <AppShellLayout>
      <Container size="xl">
        <Stack gap="xl">
          <Group>
            <Button
              variant="subtle"
              leftSection={<IconArrowLeft size={16} />}
              onClick={() => router.back()}
            >
              Back
            </Button>
            <ThemeIcon size={40} radius="xl" variant="gradient" gradient={{ from: 'pink', to: 'violet' }}>
              <IconGift size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Manage Airdrops</Title>
              <Text c="dimmed">Manage airdrops for "{decodeURIComponent(tldName)}" TLD</Text>
            </div>
          </Group>

          {/* Error State */}
          {error && (
            <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
              {error}
            </Alert>
          )}

          {/* Loading State */}
          {loading && (
            <Paper shadow="sm" p="xl" radius="md" withBorder>
              <Stack align="center" gap="md">
                <Loader size="lg" />
                <Text c="dimmed">Loading airdrop information...</Text>
              </Stack>
            </Paper>
          )}

          {/* Access Denied */}
          {!loading && !error && airdropData && !airdropData.isOwner && (
            <Alert icon={<IconShield size={16} />} title="Access Denied" color="orange">
              You are not the owner of this TLD. Only the TLD owner can manage airdrops.
              <br />
              <Text size="sm" mt="xs">
                Current owner: {formatAddress(airdropData.owner)}
              </Text>
            </Alert>
          )}

          {/* Airdrop Management Interface */}
          {!loading && !error && airdropData && airdropData.isOwner && (
            <>
              {/* Overview */}
              <Paper shadow="sm" p="lg" radius="md" withBorder>
                <Stack gap="md">
                  <Group justify="space-between">
                    <Group>
                      <ThemeIcon color="blue" size={30}>
                        <IconDatabase size={18} />
                      </ThemeIcon>
                      <div>
                        <Text size="lg" fw={500}>Airdrop Overview</Text>
                        <Text size="sm" c="dimmed">TLD: {airdropData.tldName}</Text>
                      </div>
                    </Group>
                    <Button
                      leftSection={<IconPlus size={16} />}
                      onClick={openCreateModal}
                      variant="gradient"
                      gradient={{ from: 'pink', to: 'violet' }}
                    >
                      Create Airdrop
                    </Button>
                  </Group>

                  <SimpleGrid cols={{ base: 1, sm: 3 }} gap="md">
                    <Card p="md" withBorder>
                      <Stack gap="xs">
                        <Text size="sm" c="dimmed">Total Airdrops</Text>
                        <Text size="xl" fw={700}>{airdropData.airdrops.length}</Text>
                      </Stack>
                    </Card>
                    <Card p="md" withBorder>
                      <Stack gap="xs">
                        <Text size="sm" c="dimmed">Active Airdrops</Text>
                        <Text size="xl" fw={700}>
                          {airdropData.airdrops.filter(a => a.isActive).length}
                        </Text>
                      </Stack>
                    </Card>
                    <Card p="md" withBorder>
                      <Stack gap="xs">
                        <Text size="sm" c="dimmed">Total Value</Text>
                        <Text size="xl" fw={700}>
                          {airdropData.airdrops.reduce((sum, a) => sum + Number(formatAmount(a.totalAmount)), 0).toFixed(2)} ETH
                        </Text>
                      </Stack>
                    </Card>
                  </SimpleGrid>
                </Stack>
              </Paper>

              {/* Airdrops Table */}
              <Paper shadow="sm" p="lg" radius="md" withBorder>
                <Stack gap="md">
                  <Text size="lg" fw={500}>Airdrops</Text>

                  {airdropData.airdrops.length === 0 ? (
                    <Center py="xl">
                      <Stack align="center" gap="md">
                        <ThemeIcon size={60} radius="xl" color="gray">
                          <IconGift size={30} />
                        </ThemeIcon>
                        <div style={{ textAlign: 'center' }}>
                          <Text size="lg" fw={500}>No Airdrops Created</Text>
                          <Text c="dimmed" mt="xs">
                            Create your first airdrop to reward domain holders
                          </Text>
                        </div>
                        <Button
                          leftSection={<IconPlus size={16} />}
                          onClick={openCreateModal}
                          variant="gradient"
                          gradient={{ from: 'pink', to: 'violet' }}
                        >
                          Create Airdrop
                        </Button>
                      </Stack>
                    </Center>
                  ) : (
                    <Table striped highlightOnHover>
                      <Table.Thead>
                        <Table.Tr>
                          <Table.Th>ID</Table.Th>
                          <Table.Th>Token Address</Table.Th>
                          <Table.Th>Total Amount</Table.Th>
                          <Table.Th>Per User Share</Table.Th>
                          <Table.Th>Remaining</Table.Th>
                          <Table.Th>Status</Table.Th>
                          <Table.Th>Actions</Table.Th>
                        </Table.Tr>
                      </Table.Thead>
                      <Table.Tbody>
                        {airdropData.airdrops.map((airdrop) => (
                          <Table.Tr key={airdrop.airdropId}>
                            <Table.Td>{airdrop.airdropId}</Table.Td>
                            <Table.Td>
                              <Text size="sm" family="monospace">
                                {formatAddress(airdrop.tokenAddress)}
                              </Text>
                            </Table.Td>
                            <Table.Td>{formatAmount(airdrop.totalAmount)} ETH</Table.Td>
                            <Table.Td>{formatAmount(airdrop.perUserShare)} ETH</Table.Td>
                            <Table.Td>{formatAmount(airdrop.remainingAmount)} ETH</Table.Td>
                            <Table.Td>
                              <Badge variant="light" color={airdrop.isActive ? 'green' : 'red'}>
                                {airdrop.isActive ? 'Active' : 'Inactive'}
                              </Badge>
                            </Table.Td>
                            <Table.Td>
                              <Group gap="xs">
                                {airdrop.remainingAmount > 0n && (
                                  <ActionIcon
                                    variant="light"
                                    color="blue"
                                    onClick={() => handleWithdrawAirdrop(airdrop.airdropId)}
                                    title="Withdraw remaining tokens"
                                  >
                                    <IconDownload size={16} />
                                  </ActionIcon>
                                )}
                              </Group>
                            </Table.Td>
                          </Table.Tr>
                        ))}
                      </Table.Tbody>
                    </Table>
                  )}
                </Stack>
              </Paper>
            </>
          )}

          {/* Create Airdrop Modal */}
          <Modal
            opened={createModalOpened}
            onClose={closeCreateModal}
            title="Create New Airdrop"
            size="md"
          >
            <Stack gap="md">
              <TextInput
                label="Token Contract Address"
                placeholder="0x..."
                value={newTokenAddress}
                onChange={(e) => setNewTokenAddress(e.target.value)}
                required
              />
              <TextInput
                label="Total Amount (ETH)"
                placeholder="1000"
                value={newTotalAmount}
                onChange={(e) => setNewTotalAmount(e.target.value)}
                required
              />
              <TextInput
                label="Per User Share (ETH)"
                placeholder="10"
                value={newPerUserShare}
                onChange={(e) => setNewPerUserShare(e.target.value)}
                required
              />

              <Group justify="flex-end" mt="md">
                <Button variant="outline" onClick={closeCreateModal}>
                  Cancel
                </Button>
                <Button
                  leftSection={<IconPlus size={16} />}
                  onClick={handleCreateAirdrop}
                  loading={creating}
                  disabled={!newTokenAddress || !newTotalAmount || !newPerUserShare}
                  variant="gradient"
                  gradient={{ from: 'pink', to: 'violet' }}
                >
                  Create Airdrop
                </Button>
              </Group>
            </Stack>
          </Modal>
        </Stack>
      </Container>
    </AppShellLayout>
  );
}
