'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAccount, useWalletClient } from 'wagmi';
import {
  Container,
  Title,
  Text,
  Stack,
  Paper,
  Group,
  ThemeIcon,
  Button,
  Alert,
  Card,
  Badge,
  Divider,
  Center,
  Loader,
  SimpleGrid,
  TextInput,
  Tabs,
  Textarea,
  CopyButton,
  ActionIcon,
  Tooltip,
} from '@mantine/core';
import {
  IconUser,
  IconAlertCircle,
  IconCheck,
  IconWallet,
  IconArrowLeft,
  IconCopy,
  IconSend,
  IconEdit,
  IconShield,
  IconDatabase,
  IconLink,
} from '@tabler/icons-react';
import { AppShellLayout } from '@/components/Layout/AppShell';
import { initializeSDK, sdk } from '@/lib/odude';
import { notifications } from '@mantine/notifications';
import { ethers } from 'ethers';

interface NameManagementData {
  name: string;
  tokenId: string;
  owner: string;
  isOwner: boolean;
  resolvedAddress?: string;
  tokenURI?: string;
  metadata?: Record<string, unknown>;
  approved?: string;
}

interface PageProps {
  params: Promise<{
    'sub-name': string;
  }> | {
    'sub-name': string;
  };
}

export default function NameManagementPage({ params }: PageProps) {
  const [subName, setSubName] = useState<string>('');
  
  // Handle async params (Next.js 15 compatibility)
  useEffect(() => {
    const getParams = async () => {
      try {
        const resolvedParams = params instanceof Promise ? await params : params;
        setSubName(resolvedParams['sub-name']);
      } catch (error) {
        console.error('Error resolving params:', error);
        setSubName('');
      }
    };
    getParams();
  }, [params]);

  const { isConnected, address } = useAccount();
  const { data: walletClient } = useWalletClient();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [nameData, setNameData] = useState<NameManagementData | null>(null);
  const [sdkReady, setSdkReady] = useState(false);
  
  // Form states
  const [transferTo, setTransferTo] = useState<string>('');
  const [newTokenURI, setNewTokenURI] = useState<string>('');
  const [approveAddress, setApproveAddress] = useState<string>('');
  
  // Loading states for individual operations
  const [transferring, setTransferring] = useState(false);
  const [updatingURI, setUpdatingURI] = useState(false);
  const [approving, setApproving] = useState(false);

  const fetchNameData = useCallback(async () => {
    if (!subName) return;

    try {
      setLoading(true);
      setError(null);

      const decodedName = decodeURIComponent(subName);
      console.log('Fetching name management data for:', decodedName);

      // Get name information
      const nameInfo = await sdk.getNameInfo(decodedName);
      
      if (!nameInfo || !nameInfo.tokenId) {
        throw new Error('Name not found or not registered');
      }

      // Get additional registry information
      const [owner, tokenURI, approved] = await Promise.all([
        sdk.registry().ownerOf(nameInfo.tokenId),
        sdk.registry().tokenURI(nameInfo.tokenId).catch(() => ''),
        sdk.registry().getApproved(nameInfo.tokenId).catch(() => ''),
      ]);

      const isOwner = address?.toLowerCase() === owner.toLowerCase();

      const managementData: NameManagementData = {
        name: decodedName,
        tokenId: nameInfo.tokenId.toString(),
        owner,
        isOwner,
        resolvedAddress: nameInfo.resolvedAddress,
        tokenURI: tokenURI || undefined,
        metadata: nameInfo.metadata,
        approved: approved || undefined,
      };

      setNameData(managementData);
      
      // Set form initial values
      setNewTokenURI(tokenURI || '');

    } catch (error) {
      console.error('Failed to fetch name data:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch name information');
    } finally {
      setLoading(false);
    }
  }, [subName, address]);

  useEffect(() => {
    if (!isConnected) {
      setError('Please connect your wallet to manage this name');
      return;
    }

    initializeSDK()
      .then(() => setSdkReady(true))
      .catch((err) => setError(err.message));
  }, [isConnected, router]);

  useEffect(() => {
    if (sdkReady && subName) {
      fetchNameData();
    }
  }, [sdkReady, subName, fetchNameData]);

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const handleTransfer = async () => {
    if (!nameData || !walletClient || !transferTo) {
      notifications.show({
        title: 'Invalid Input',
        message: 'Please enter a valid address to transfer to',
        color: 'red',
      });
      return;
    }

    if (!ethers.isAddress(transferTo)) {
      notifications.show({
        title: 'Invalid Address',
        message: 'Please enter a valid Ethereum address',
        color: 'red',
      });
      return;
    }

    setTransferring(true);
    try {
      // Connect the wallet signer to the SDK
      sdk.connectSigner(walletClient);

      const tx = await sdk.registry().safeTransferFrom(
        nameData.owner,
        transferTo,
        nameData.tokenId
      );

      await tx.wait();

      notifications.show({
        title: 'Transfer Successful',
        message: `Name transferred to ${formatAddress(transferTo)}`,
        color: 'green',
      });

      // Refresh data
      await fetchNameData();
      setTransferTo('');

    } catch (error) {
      console.error('Failed to transfer name:', error);
      notifications.show({
        title: 'Transfer Failed',
        message: error instanceof Error ? error.message : 'Failed to transfer name',
        color: 'red',
      });
    } finally {
      setTransferring(false);
    }
  };

  const handleUpdateTokenURI = async () => {
    if (!nameData || !walletClient || !newTokenURI) {
      notifications.show({
        title: 'Invalid Input',
        message: 'Please enter a valid token URI',
        color: 'red',
      });
      return;
    }

    setUpdatingURI(true);
    try {
      // Connect the wallet signer to the SDK
      sdk.connectSigner(walletClient);

      const tx = await sdk.registry().setTokenURI(nameData.tokenId, newTokenURI);
      await tx.wait();

      notifications.show({
        title: 'Token URI Updated',
        message: 'Token URI updated successfully',
        color: 'green',
      });

      // Refresh data
      await fetchNameData();

    } catch (error) {
      console.error('Failed to update token URI:', error);
      notifications.show({
        title: 'Update Failed',
        message: error instanceof Error ? error.message : 'Failed to update token URI',
        color: 'red',
      });
    } finally {
      setUpdatingURI(false);
    }
  };

  const handleApprove = async () => {
    if (!nameData || !walletClient || !approveAddress) {
      notifications.show({
        title: 'Invalid Input',
        message: 'Please enter a valid address to approve',
        color: 'red',
      });
      return;
    }

    if (!ethers.isAddress(approveAddress)) {
      notifications.show({
        title: 'Invalid Address',
        message: 'Please enter a valid Ethereum address',
        color: 'red',
      });
      return;
    }

    setApproving(true);
    try {
      // Connect the wallet signer to the SDK
      sdk.connectSigner(walletClient);

      const tx = await sdk.registry().approve(approveAddress, nameData.tokenId);
      await tx.wait();

      notifications.show({
        title: 'Approval Successful',
        message: `Approved ${formatAddress(approveAddress)} to manage this name`,
        color: 'green',
      });

      // Refresh data
      await fetchNameData();
      setApproveAddress('');

    } catch (error) {
      console.error('Failed to approve address:', error);
      notifications.show({
        title: 'Approval Failed',
        message: error instanceof Error ? error.message : 'Failed to approve address',
        color: 'red',
      });
    } finally {
      setApproving(false);
    }
  };

  if (!isConnected || !subName) {
    return null; // Will redirect or still loading params
  }

  return (
    <AppShellLayout>
      <Container size="xl">
        <Stack gap="xl">
          <Group>
            <Button
              variant="subtle"
              leftSection={<IconArrowLeft size={16} />}
              onClick={() => router.back()}
            >
              Back
            </Button>
            <ThemeIcon size={40} radius="xl" variant="gradient" gradient={{ from: 'blue', to: 'cyan' }}>
              <IconUser size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Manage Name</Title>
              <Text c="dimmed">Manage settings for "{decodeURIComponent(subName)}"</Text>
            </div>
          </Group>

          {/* Error State */}
          {error && (
            <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
              {error}
            </Alert>
          )}

          {/* Loading State */}
          {loading && (
            <Paper shadow="sm" p="xl" radius="md" withBorder>
              <Stack align="center" gap="md">
                <Loader size="lg" />
                <Text c="dimmed">Loading name information...</Text>
              </Stack>
            </Paper>
          )}

          {/* Access Denied */}
          {!loading && !error && nameData && !nameData.isOwner && (
            <Alert icon={<IconShield size={16} />} title="Access Denied" color="orange">
              You are not the owner of this name. Only the name owner can manage these settings.
              <br />
              <Text size="sm" mt="xs">
                Current owner: {formatAddress(nameData.owner)}
              </Text>
            </Alert>
          )}

          {/* Name Management Interface */}
          {!loading && !error && nameData && nameData.isOwner && (
            <>
              {/* Name Overview */}
              <Paper shadow="sm" p="lg" radius="md" withBorder>
                <Stack gap="md">
                  <Group>
                    <ThemeIcon color="blue" size={30}>
                      <IconDatabase size={18} />
                    </ThemeIcon>
                    <div>
                      <Text size="lg" fw={500}>Name Information</Text>
                      <Text size="sm" c="dimmed">Basic details about your name</Text>
                    </div>
                  </Group>

                  <SimpleGrid cols={{ base: 1, sm: 2 }} gap="md">
                    <Card p="md" withBorder>
                      <Stack gap="xs">
                        <Text size="sm" c="dimmed">Name</Text>
                        <Group gap="xs">
                          <Text size="lg" fw={500} family="monospace">{nameData.name}</Text>
                          <CopyButton value={nameData.name}>
                            {({ copied, copy }) => (
                              <Tooltip label={copied ? 'Copied' : 'Copy name'}>
                                <ActionIcon variant="subtle" onClick={copy}>
                                  <IconCopy size={16} />
                                </ActionIcon>
                              </Tooltip>
                            )}
                          </CopyButton>
                        </Group>
                      </Stack>
                    </Card>
                    <Card p="md" withBorder>
                      <Stack gap="xs">
                        <Text size="sm" c="dimmed">Token ID</Text>
                        <Group gap="xs">
                          <Text size="lg" fw={500} family="monospace">{nameData.tokenId}</Text>
                          <CopyButton value={nameData.tokenId}>
                            {({ copied, copy }) => (
                              <Tooltip label={copied ? 'Copied' : 'Copy token ID'}>
                                <ActionIcon variant="subtle" onClick={copy}>
                                  <IconCopy size={16} />
                                </ActionIcon>
                              </Tooltip>
                            )}
                          </CopyButton>
                        </Group>
                      </Stack>
                    </Card>
                    <Card p="md" withBorder>
                      <Stack gap="xs">
                        <Text size="sm" c="dimmed">Owner</Text>
                        <Group gap="xs">
                          <Text size="lg" fw={500} family="monospace">{formatAddress(nameData.owner)}</Text>
                          <CopyButton value={nameData.owner}>
                            {({ copied, copy }) => (
                              <Tooltip label={copied ? 'Copied' : 'Copy owner address'}>
                                <ActionIcon variant="subtle" onClick={copy}>
                                  <IconCopy size={16} />
                                </ActionIcon>
                              </Tooltip>
                            )}
                          </CopyButton>
                        </Group>
                      </Stack>
                    </Card>
                    <Card p="md" withBorder>
                      <Stack gap="xs">
                        <Text size="sm" c="dimmed">Resolved Address</Text>
                        <Group gap="xs">
                          <Text size="lg" fw={500} family="monospace">
                            {nameData.resolvedAddress ? formatAddress(nameData.resolvedAddress) : 'Not set'}
                          </Text>
                          {nameData.resolvedAddress && (
                            <CopyButton value={nameData.resolvedAddress}>
                              {({ copied, copy }) => (
                                <Tooltip label={copied ? 'Copied' : 'Copy resolved address'}>
                                  <ActionIcon variant="subtle" onClick={copy}>
                                    <IconCopy size={16} />
                                  </ActionIcon>
                                </Tooltip>
                              )}
                            </CopyButton>
                          )}
                        </Group>
                      </Stack>
                    </Card>
                  </SimpleGrid>
                </Stack>
              </Paper>

              {/* Management Actions */}
              <Tabs defaultValue="transfer" variant="outline">
                <Tabs.List>
                  <Tabs.Tab value="transfer" leftSection={<IconSend size={16} />}>
                    Transfer
                  </Tabs.Tab>
                  <Tabs.Tab value="metadata" leftSection={<IconEdit size={16} />}>
                    Metadata
                  </Tabs.Tab>
                  <Tabs.Tab value="approve" leftSection={<IconShield size={16} />}>
                    Approve
                  </Tabs.Tab>
                </Tabs.List>

                <Tabs.Panel value="transfer" pt="md">
                  <Paper shadow="sm" p="lg" radius="md" withBorder>
                    <Stack gap="md">
                      <div>
                        <Text size="lg" fw={500}>Transfer Name</Text>
                        <Text size="sm" c="dimmed">
                          Transfer ownership of this name to another address
                        </Text>
                      </div>

                      <TextInput
                        label="Transfer to Address"
                        placeholder="0x..."
                        value={transferTo}
                        onChange={(e) => setTransferTo(e.target.value)}
                        description="Enter the Ethereum address to transfer this name to"
                      />

                      <Group justify="flex-end">
                        <Button
                          leftSection={<IconSend size={16} />}
                          onClick={handleTransfer}
                          loading={transferring}
                          disabled={!transferTo || !ethers.isAddress(transferTo)}
                          color="red"
                          variant="outline"
                        >
                          Transfer Name
                        </Button>
                      </Group>
                    </Stack>
                  </Paper>
                </Tabs.Panel>

                <Tabs.Panel value="metadata" pt="md">
                  <Paper shadow="sm" p="lg" radius="md" withBorder>
                    <Stack gap="md">
                      <div>
                        <Text size="lg" fw={500}>Update Token URI</Text>
                        <Text size="sm" c="dimmed">
                          Update the metadata URI for this name
                        </Text>
                      </div>

                      <Textarea
                        label="Token URI"
                        placeholder="https://... or data:application/json,..."
                        value={newTokenURI}
                        onChange={(e) => setNewTokenURI(e.target.value)}
                        description="Enter the new token URI (JSON metadata or IPFS link)"
                        minRows={3}
                      />

                      <Group justify="flex-end">
                        <Button
                          leftSection={<IconEdit size={16} />}
                          onClick={handleUpdateTokenURI}
                          loading={updatingURI}
                          disabled={!newTokenURI || newTokenURI === nameData.tokenURI}
                          variant="gradient"
                          gradient={{ from: 'blue', to: 'cyan' }}
                        >
                          Update URI
                        </Button>
                      </Group>
                    </Stack>
                  </Paper>
                </Tabs.Panel>

                <Tabs.Panel value="approve" pt="md">
                  <Paper shadow="sm" p="lg" radius="md" withBorder>
                    <Stack gap="md">
                      <div>
                        <Text size="lg" fw={500}>Approve Address</Text>
                        <Text size="sm" c="dimmed">
                          Grant another address permission to manage this name
                        </Text>
                      </div>

                      {nameData.approved && (
                        <Alert color="blue" title="Currently Approved">
                          <Group gap="xs">
                            <Text size="sm">
                              {formatAddress(nameData.approved)} is currently approved to manage this name
                            </Text>
                            <CopyButton value={nameData.approved}>
                              {({ copied, copy }) => (
                                <ActionIcon variant="subtle" size="sm" onClick={copy}>
                                  <IconCopy size={12} />
                                </ActionIcon>
                              )}
                            </CopyButton>
                          </Group>
                        </Alert>
                      )}

                      <TextInput
                        label="Approve Address"
                        placeholder="0x..."
                        value={approveAddress}
                        onChange={(e) => setApproveAddress(e.target.value)}
                        description="Enter the address to approve for managing this name"
                      />

                      <Group justify="flex-end">
                        <Button
                          leftSection={<IconShield size={16} />}
                          onClick={handleApprove}
                          loading={approving}
                          disabled={!approveAddress || !ethers.isAddress(approveAddress)}
                          variant="outline"
                        >
                          Approve Address
                        </Button>
                      </Group>
                    </Stack>
                  </Paper>
                </Tabs.Panel>
              </Tabs>
            </>
          )}
        </Stack>
      </Container>
    </AppShellLayout>
  );
}
