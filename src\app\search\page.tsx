'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAccount, useWalletClient, useBalance } from 'wagmi';
import {
  Container,
  Title,
  Text,
  Stack,
  Paper,
  Group,
  ThemeIcon,
  TextInput,
  Button,
  Alert,
  Card,
  Badge,
  Divider,
  Center,
  Loader,
  ActionIcon,
} from '@mantine/core';
import {
  IconSearch,
  IconAlertCircle,
  IconCheck,
  IconX,
  IconCoin,
  IconWallet,
  IconCopy,
  IconExternalLink,
} from '@tabler/icons-react';
import { AppShellLayout } from '@/components/Layout/AppShell';
import { initializeSDK, sdk } from '@/lib/odude';
import { useClipboard } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';

interface SearchResult {
  name: string;
  exists: boolean;
  owner?: string;
  resolvedAddress?: string;
  tokenId?: string;
  metadata?: Record<string, unknown>;
  eligibility?: {
    eligible: boolean;
    available: boolean;
    tldActive: boolean;
    cost: bigint;
    reason: string;
  };
  tldInfo?: {
    name: string;
    tokenId?: string;
    commission?: number;
    isActive?: boolean;
    owner?: string;
  };
  tldNotRegistered?: boolean;
  suggestedTld?: string;
  isTldSearch?: boolean;
}

export default function Search() {
  const { address, isConnected } = useAccount();
  const { data: walletClient } = useWalletClient();
  const { data: balance } = useBalance({ address });
  const router = useRouter();
  const clipboard = useClipboard({ timeout: 500 });

  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [minting, setMinting] = useState(false);
  const [result, setResult] = useState<SearchResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      notifications.show({
        title: 'Invalid Input',
        message: 'Please enter a domain name to search',
        color: 'orange',
      });
      return;
    }

    if (!isConnected) {
      notifications.show({
        title: 'Wallet Not Connected',
        message: 'Please connect your wallet to search for names',
        color: 'orange',
      });
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Initialize SDK if needed
      await initializeSDK();

      const normalizedName = searchQuery.toLowerCase().trim();

      // Check if this is a TLD-only search (no @ symbol)
      const isTldOnlySearch = !normalizedName.includes('@');

      if (isTldOnlySearch) {
        // Handle TLD-only search (e.g., "crypto1")
        let searchResult: SearchResult = {
          name: normalizedName,
          exists: false,
          isTldSearch: true,
        };

        try {
          // Check if TLD exists
          await sdk.getTldInfo(normalizedName);
          searchResult.tldInfo = {
            name: normalizedName,
            commission: 0, // Will be fetched separately if needed
          };
          searchResult.exists = true;
        } catch (err) {
          console.error('TLD not found:', err);
          // TLD doesn't exist, offer to mint it
          searchResult.tldNotRegistered = true;
          searchResult.suggestedTld = normalizedName;
        }

        setResult(searchResult);
        return;
      }

      // Regular domain search (name@tld format)
      const exists = await sdk.resolver().nameExists(normalizedName);

      let searchResult: SearchResult = {
        name: normalizedName,
        exists,
      };

      if (exists) {
        // Get existing name details
        try {
          const nameInfo = await sdk.getNameInfo(normalizedName);
          searchResult = {
            ...searchResult,
            owner: nameInfo.owner,
            resolvedAddress: nameInfo.resolvedAddress,
            tokenId: nameInfo.tokenId?.toString(),
            metadata: nameInfo.metadata,
          };
        } catch (err) {
          console.error('Failed to get name info:', err);
        }
      } else {
        // Check minting eligibility for available names
        try {
          const eligibility = await sdk.checkMintEligibility(normalizedName);
          searchResult.eligibility = eligibility;

          // Get TLD info if eligible
          if (eligibility.eligible) {
            const tldName = normalizedName.split('@')[1];
            if (tldName) {
              try {
                await sdk.getTldInfo(tldName);
                searchResult.tldInfo = {
                  name: tldName,
                  commission: 0, // Will be fetched separately if needed
                };
              } catch (err) {
                console.error('Failed to get TLD info:', err);
                // If TLD info fails, it might be an unregistered TLD
                searchResult.tldNotRegistered = true;
              }
            }
          } else {
            // Check if the issue is an unregistered TLD
            const tldName = normalizedName.split('@')[1];
            if (tldName && eligibility.reason?.includes('TLD')) {
              searchResult.tldNotRegistered = true;
              searchResult.suggestedTld = tldName;
            }
          }
        } catch (err) {
          console.error('Failed to check eligibility:', err);
          // Check if this might be a TLD registration issue
          const tldName = normalizedName.split('@')[1];
          if (tldName && err instanceof Error && err.message.includes('TLD')) {
            searchResult.tldNotRegistered = true;
            searchResult.suggestedTld = tldName;
          }
        }
      }

      setResult(searchResult);
    } catch (err) {
      console.error('Search failed:', err);
      setError(err instanceof Error ? err.message : 'Search failed');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyAddress = (addr: string) => {
    clipboard.copy(addr);
    notifications.show({
      title: 'Address Copied',
      message: 'Address copied to clipboard',
      color: 'green',
    });
  };

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const formatEther = (wei: bigint) => {
    return (Number(wei) / 1e18).toFixed(4);
  };

  const handleMint = async () => {
    if (!result || !result.eligibility?.eligible || !walletClient || !address) {
      notifications.show({
        title: 'Cannot Mint',
        message: 'Wallet not connected or name not eligible for minting',
        color: 'red',
      });
      return;
    }

    // Check if wallet has sufficient balance
    if (balance && result.eligibility.cost > balance.value) {
      notifications.show({
        title: 'Insufficient Balance',
        message: `You need ${formatEther(result.eligibility.cost)} ETH but only have ${balance.formatted} ETH`,
        color: 'red',
      });
      return;
    }

    setMinting(true);

    try {
      // Initialize SDK if needed
      await initializeSDK();

      notifications.show({
        title: 'Minting Started',
        message: 'Please confirm the transaction in your wallet',
        color: 'blue',
      });

      // Mint the name using the correct SDK method
      const tx = await sdk.tld().mintDomain(
        result.name,
        address,
        {
          value: result.eligibility.cost,
        }
      );

      // Show transaction submitted notification
      notifications.show({
        title: 'Transaction Submitted',
        message: `Transaction hash: ${tx.hash}`,
        color: 'blue',
        autoClose: false,
      });

      // Wait for transaction confirmation
      const receipt = await tx.wait();

      notifications.show({
        title: 'Minting Successful!',
        message: `Successfully minted ${result.name}${receipt ? `. Block: ${receipt.blockNumber}` : ''}`,
        color: 'green',
        autoClose: 10000,
      });

      // Redirect to the info page for the newly minted name
      router.push(`/info/${encodeURIComponent(result.name)}`);

    } catch (error) {
      console.error('Minting failed:', error);

      let errorMessage = 'Failed to mint name';
      if (error instanceof Error) {
        if (error.message.includes('user rejected')) {
          errorMessage = 'Transaction was cancelled by user';
        } else if (error.message.includes('insufficient funds')) {
          errorMessage = 'Insufficient funds for transaction';
        } else if (error.message.includes('gas')) {
          errorMessage = 'Transaction failed due to gas issues';
        } else {
          errorMessage = error.message;
        }
      }

      notifications.show({
        title: 'Minting Failed',
        message: errorMessage,
        color: 'red',
        autoClose: 10000,
      });
    } finally {
      setMinting(false);
    }
  };

  return (
    <AppShellLayout>
      <Container size="md">
        <Stack gap="xl">
          <Group>
            <ThemeIcon size={40} radius="xl" variant="gradient" gradient={{ from: 'green', to: 'teal' }}>
              <IconSearch size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Search Names</Title>
              <Text c="dimmed">Find and mint ODude domain names</Text>
            </div>
          </Group>

          {!isConnected && (
            <Alert
              icon={<IconWallet size={16} />}
              title="Wallet Connection Required"
              color="orange"
            >
              Please connect your wallet to search and mint domain names.
            </Alert>
          )}

          {/* Search Input */}
          <Paper shadow="sm" p="lg" radius="md" withBorder>
            <Stack gap="md">
              <TextInput
                placeholder="Enter domain name (e.g., alice@crypto, bob@fil)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                size="lg"
                leftSection={<IconSearch size={20} />}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Group justify="flex-end">
                <Button
                  onClick={handleSearch}
                  loading={loading}
                  leftSection={<IconSearch size={16} />}
                  size="md"
                >
                  Search
                </Button>
              </Group>
            </Stack>
          </Paper>

          {/* Loading State */}
          {loading && (
            <Center py="xl">
              <Stack align="center" gap="md">
                <Loader size="lg" />
                <Text c="dimmed">Searching...</Text>
              </Stack>
            </Center>
          )}

          {/* Error State */}
          {error && (
            <Alert icon={<IconAlertCircle size={16} />} title="Search Error" color="red">
              {error}
            </Alert>
          )}

          {/* Search Results */}
          {result && !loading && (
            <Paper shadow="sm" p="lg" radius="md" withBorder>
              <Stack gap="lg">
                <Group justify="space-between">
                  <div>
                    <Text size="xl" fw={700}>{result.name}</Text>
                    <Group gap="xs" mt="xs">
                      {result.exists ? (
                        <Badge color="red" variant="light" leftSection={<IconX size={14} />}>
                          Taken
                        </Badge>
                      ) : (
                        <Badge color="green" variant="light" leftSection={<IconCheck size={14} />}>
                          Available
                        </Badge>
                      )}
                    </Group>
                  </div>
                </Group>

                <Divider />

                {result.exists ? (
                  /* Existing Name Details */
                  <Stack gap="md">
                    <Text size="lg" fw={500}>Name Details</Text>

                    {result.owner && (
                      <Group justify="space-between">
                        <Text c="dimmed">Owner:</Text>
                        <Group gap="xs">
                          <Text fw={500}>{formatAddress(result.owner)}</Text>
                          <ActionIcon
                            size="sm"
                            variant="subtle"
                            onClick={() => handleCopyAddress(result.owner!)}
                          >
                            <IconCopy size={14} />
                          </ActionIcon>
                        </Group>
                      </Group>
                    )}

                    {result.resolvedAddress && (
                      <Group justify="space-between">
                        <Text c="dimmed">Resolves to:</Text>
                        <Group gap="xs">
                          <Text fw={500}>{formatAddress(result.resolvedAddress)}</Text>
                          <ActionIcon
                            size="sm"
                            variant="subtle"
                            onClick={() => handleCopyAddress(result.resolvedAddress!)}
                          >
                            <IconCopy size={14} />
                          </ActionIcon>
                        </Group>
                      </Group>
                    )}

                    {result.tokenId && (
                      <Group justify="space-between">
                        <Text c="dimmed">Token ID:</Text>
                        <Text fw={500}>{result.tokenId}</Text>
                      </Group>
                    )}

                    {result.metadata?.description && (
                      <div>
                        <Text c="dimmed">Description:</Text>
                        <Text>{String(result.metadata.description)}</Text>
                      </div>
                    )}

                    <Group justify="center" mt="md">
                      <Button
                        size="lg"
                        variant="outline"
                        leftSection={<IconExternalLink size={18} />}
                        onClick={() => router.push(`/info/${encodeURIComponent(result.name)}`)}
                      >
                        View Details
                      </Button>
                    </Group>
                  </Stack>
                ) : result.tldNotRegistered ? (
                  /* TLD Not Registered */
                  <Stack gap="md">
                    <Text size="lg" fw={500}>TLD Registration</Text>

                    <Alert icon={<IconAlertCircle size={16} />} title="TLD Not Registered" color="orange">
                      The TLD &quot;{result.suggestedTld}&quot; is not yet registered. You can mint it to become the owner and earn commission from domain registrations.
                    </Alert>

                    <Group justify="center">
                      <Button
                        size="lg"
                        variant="gradient"
                        gradient={{ from: 'orange', to: 'red' }}
                        leftSection={<IconCoin size={18} />}
                        onClick={() => {
                          router.push(`/mint/${encodeURIComponent(result.suggestedTld || result.name)}`);
                        }}
                      >
                        Mint TLD &quot;{result.suggestedTld || result.name}&quot;
                      </Button>
                    </Group>
                  </Stack>
                ) : (
                  /* Available Name - Minting Info */
                  <Stack gap="md">
                    <Text size="lg" fw={500}>Minting Information</Text>

                    {result.eligibility ? (
                      result.eligibility.eligible ? (
                        <Stack gap="md">
                          <Alert icon={<IconCheck size={16} />} title="Available for Minting" color="green">
                            This name is available and can be minted!
                          </Alert>

                          <Card p="md" withBorder>
                            <Stack gap="xs">
                              <Group justify="space-between">
                                <Text c="dimmed">Minting Cost:</Text>
                                <Group gap="xs">
                                  <IconCoin size={16} />
                                  <Text fw={500}>{formatEther(result.eligibility.cost)} ETH</Text>
                                </Group>
                              </Group>

                              {balance && (
                                <Group justify="space-between">
                                  <Text c="dimmed">Wallet Balance:</Text>
                                  <Group gap="xs">
                                    <IconWallet size={16} />
                                    <Text fw={500} c={balance.value >= result.eligibility.cost ? 'green' : 'red'}>
                                      {Number(balance.value) / 1e18} {balance.symbol}
                                    </Text>
                                  </Group>
                                </Group>
                              )}

                              {result.tldInfo && (
                                <>
                                  <Group justify="space-between">
                                    <Text c="dimmed">TLD:</Text>
                                    <Text fw={500}>{result.tldInfo.name}</Text>
                                  </Group>

                                  <Group justify="space-between">
                                    <Text c="dimmed">Commission:</Text>
                                    <Text fw={500}>{result.tldInfo.commission}%</Text>
                                  </Group>
                                </>
                              )}
                            </Stack>
                          </Card>

                          <Group justify="center">
                            <Button
                              size="lg"
                              variant="gradient"
                              gradient={{ from: 'green', to: 'teal' }}
                              leftSection={<IconWallet size={18} />}
                              onClick={handleMint}
                              loading={minting}
                              disabled={minting || (balance && balance.value < result.eligibility.cost)}
                            >
                              {minting
                                ? 'Minting...'
                                : balance && balance.value < result.eligibility.cost
                                  ? 'Insufficient Balance'
                                  : `Mint Name (${formatEther(result.eligibility.cost)} ETH)`
                              }
                            </Button>
                          </Group>
                        </Stack>
                      ) : (
                        <Alert icon={<IconX size={16} />} title="Cannot Mint" color="red">
                          {result.eligibility.reason}
                        </Alert>
                      )
                    ) : (
                      <Alert icon={<IconAlertCircle size={16} />} title="Unknown Status" color="orange">
                        Could not determine minting eligibility
                      </Alert>
                    )}
                  </Stack>
                )}
              </Stack>
            </Paper>
          )}

          {/* Search Tips */}
          <Paper p="md" radius="md" withBorder>
            <Stack gap="xs">
              <Text size="sm" fw={500}>Search Tips:</Text>
              <Text size="xs" c="dimmed">• Use format: name@tld (e.g., alice@crypto, bob@fil)</Text>
              <Text size="xs" c="dimmed">• Search TLD only: crypto1, fil, bnb (to check/mint TLD)</Text>
              <Text size="xs" c="dimmed">• Supported TLDs: @crypto, @fil, @fvm, @bnb, @binance</Text>
              <Text size="xs" c="dimmed">• Names are case-insensitive</Text>
              <Text size="xs" c="dimmed">• Only Base Sepolia network is currently active</Text>
            </Stack>
          </Paper>
        </Stack>
      </Container>
    </AppShellLayout>
  );
}
